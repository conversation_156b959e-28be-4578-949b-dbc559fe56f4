'use client'
import Link from 'next/link'
import { useState } from 'react'

export default function Section10() {
    const [isActive, setIsActive] = useState({
        status: false,
        key: 1,
    })

    // FAQ data as JavaScript object
    const faqData = {
  leftColumn: [
    {
      id: 1,
      question: "What services do you offer as a digital marketing agency in Ahmedabad?",
      answer:
        "We help Ahmedabad businesses with SEO, paid ads, content marketing, automation, and CRM integration—all designed to attract leads and drive sales.",
    },
    {
      id: 2,
      question: "Is your website development agency in Ahmedabad trusted by brands?",
      answer:
        "Yes, because we focus on speed, design, SEO-readiness, and conversion. Our web development team creates powerful sites that perform on every device.",
    },
    {
      id: 3,
      question: "Do you provide WhatsApp automation for Ahmedabad-based businesses?",
      answer:
        "Yes—we’re a WhatsApp automation company in Ahmedabad helping businesses manage inquiries, send broadcast updates, and convert prospects using chatbots.",
    },
  ],
  rightColumn: [
    {
      id: 4,
      question: "What are your marketing automation services in Ahmedabad?",
      answer:
        "Our marketing automation includes drip emails, WhatsApp flows, CRM tasks, lead scoring, and follow-ups—all automated to help Ahmedabad businesses scale.",
    },
    {
      id: 5,
      question: "Are your SEO strategies suited for local businesses in Ahmedabad?",
      answer:
        "Absolutely. As the best SEO company in Ahmedabad, we specialize in local search optimization, Google Maps ranking, and geo-targeted content.",
    },
    {
      id: 6,
      question: "Can you help my Ahmedabad business with leads and automation?",
      answer:
        "Yes. We provide complete inbound and outbound lead funnels using a mix of SEO, paid ads, automation, and WhatsApp API integration.",
    },
  ],
};


    const handleClick = (key) => {
        if (isActive.key === key) {
            setIsActive({
                status: false,
            })
        } else {
            setIsActive({
                status: true,
                key,
            })
        }
    }

    // Function to render FAQ items
    const renderFAQItem = (item, accordionId) => (
        <div className="accordion-item" key={item.id}>
            <h2 className="accordion-header" id={`flush-heading${item.id}`} onClick={() => handleClick(item.id)}>
                <button 
                    className={isActive.key === item.id ? "accordion-button" : "accordion-button collapsed"}
                    type="button" 
                    data-bs-toggle="collapse" 
                    data-bs-target={`#flush-collapse${item.id}`} 
                    aria-expanded="false" 
                    aria-controls={`flush-collapse${item.id}`}
                >
                    {item.question}
                </button>
            </h2>
            <div 
                className={isActive.key === item.id ? "accordion-collapse collapse show" : "accordion-collapse collapse"} 
                id={`flush-collapse${item.id}`} 
                aria-labelledby={`flush-heading${item.id}`} 
                data-bs-parent={`#${accordionId}`}
            >
                <div className="accordion-body">
                    <p>{item.answer}</p>
                </div>
            </div>
        </div>
    )

    return (
        <>
            <section className="section-box box-faqs">
                <div className="container">
                    <div className="text-center mb-70">
                        <h3 className="heading-2 mb-20">Frequently Asked Questions</h3>
                        
                    </div>
                    <div className="box-2-col-faqs">
                        {/* Left Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS">
                                {faqData.leftColumn.map(item => renderFAQItem(item, 'accordionFAQS'))}
                            </div>
                        </div>
                        
                        {/* Right Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS2">
                                {faqData.rightColumn.map(item => renderFAQItem(item, 'accordionFAQS2'))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}