'use client'
import Link from 'next/link'
import { useState } from 'react'

export default function Section10() {
    const [isActive, setIsActive] = useState({
        status: false,
        key: 1,
    })

  const faqData = {
  leftColumn: [
    {
      id: 1,
      question: "What makes you the best WhatsApp automation company in Hyderabad?",
      answer:
        "Our Hyderabad team builds conversational WhatsApp flows that capture leads, provide support, and close sales—automatically and at scale.",
    },
    {
      id: 2,
      question: "Are you an SEO agency in Hyderabad with proven results?",
      answer:
        "Yes—we’re a top SEO agency in Hyderabad focused on technical SEO, content optimization, and local ranking. We don’t just promise traffic; we deliver conversions.",
    },
    {
      id: 3,
      question: "Do you build websites for Hyderabad-based companies?",
      answer:
        "Absolutely. We’re a leading website development company in Hyderabad, creating responsive and SEO-rich websites tailored for local and global audiences.",
    },
    {
      id: 4,
      question: "What’s the difference between your website designing company and other agencies in Hyderabad?",
      answer:
        "We combine UI/UX expertise with SEO strategy and automation integration. That’s why Hyderabad startups and businesses trust us for performance-focused design.",
    },
  ],
  rightColumn: [
    {
      id: 5,
      question: "Do you offer full-stack digital marketing services in Hyderabad?",
      answer:
        "Yes—from SEO and PPC to WhatsApp campaigns and CRM automation, we are a one-stop digital marketing company in Hyderabad.",
    },
    {
      id: 6,
      question: "Can I use your WhatsApp API services in Hyderabad for eCommerce?",
      answer:
        "Yes, we’re a trusted WhatsApp API provider in Hyderabad. We help eCommerce and service businesses send order updates, recover carts, and support customers instantly.",
    },
    {
      id: 7,
      question: "What industries do you serve as a digital marketing agency in Hyderabad?",
      answer:
        "From real estate and healthcare to SaaS and education, we serve a wide range of Hyderabad industries with scalable SEO and automation strategies.",
    },
  ],
};



    const handleClick = (key) => {
        if (isActive.key === key) {
            setIsActive({
                status: false,
            })
        } else {
            setIsActive({
                status: true,
                key,
            })
        }
    }

    // Function to render FAQ items
    const renderFAQItem = (item, accordionId) => (
        <div className="accordion-item" key={item.id}>
            <h2 className="accordion-header" id={`flush-heading${item.id}`} onClick={() => handleClick(item.id)}>
                <button 
                    className={isActive.key === item.id ? "accordion-button" : "accordion-button collapsed"}
                    type="button" 
                    data-bs-toggle="collapse" 
                    data-bs-target={`#flush-collapse${item.id}`} 
                    aria-expanded="false" 
                    aria-controls={`flush-collapse${item.id}`}
                >
                    {item.question}
                </button>
            </h2>
            <div 
                className={isActive.key === item.id ? "accordion-collapse collapse show" : "accordion-collapse collapse"} 
                id={`flush-collapse${item.id}`} 
                aria-labelledby={`flush-heading${item.id}`} 
                data-bs-parent={`#${accordionId}`}
            >
                <div className="accordion-body">
                    <p>{item.answer}</p>
                </div>
            </div>
        </div>
    )

    return (
        <>
            <section className="section-box box-faqs">
                <div className="container">
                    <div className="text-center mb-70">
                        <h3 className="heading-2 mb-20">Frequently Asked Questions</h3>
                        
                    </div>
                    <div className="box-2-col-faqs">
                        {/* Left Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS">
                                {faqData.leftColumn.map(item => renderFAQItem(item, 'accordionFAQS'))}
                            </div>
                        </div>
                        
                        {/* Right Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS2">
                                {faqData.rightColumn.map(item => renderFAQItem(item, 'accordionFAQS2'))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}