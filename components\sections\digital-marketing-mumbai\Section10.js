'use client'
import Link from 'next/link'
import { useState } from 'react'

export default function Section10() {
    const [isActive, setIsActive] = useState({
        status: false,
        key: 1,
    })

const faqData = {
  leftColumn: [
    {
      id: 1,
      question: "How does your WhatsApp automation company in Mumbai help with customer engagement?",
      answer:
        "Our Mumbai clients use WhatsApp automation to reply instantly, qualify leads, and even book appointments—resulting in higher retention and faster deal closures.",
    },
    {
      id: 2,
      question: "What’s included in your SEO services in Mumbai?",
      answer:
        "We offer on-page SEO, technical fixes, keyword mapping for Mumbai searches, and backlink strategies—plus analytics and reporting to measure results.",
    },
    {
      id: 3,
      question: "Do you offer web development services for Mumbai businesses?",
      answer:
        "Yes. As a Mumbai-based website development company, we build high-performance, mobile-friendly, and SEO-optimized sites tailored to your business needs.",
    },
    {
      id: 4,
      question: "Are you a full-stack digital marketing agency in Mumbai?",
      answer:
        "Definitely. We provide SEO, social media, paid advertising, WhatsApp automations, and CRM integrations—everything you need to dominate Mumbai’s digital market.",
    },
  ],
  rightColumn: [
    {
      id: 5,
      question: "What sets you apart from other lead generation companies in Mumbai?",
      answer:
        "We don’t just generate leads—we qualify and nurture them using WhatsApp bots, AI flows, and retargeting. That’s why Mumbai brands trust us with performance marketing.",
    },
    {
      id: 6,
      question: "Can I integrate WhatsApp API into my eCommerce or local business in Mumbai?",
      answer:
        "Yes. As an official WhatsApp API provider in Mumbai, we can integrate it with your website, CRM, or funnel system to ensure every inquiry gets a response.",
    },
    {
      id: 7,
      question: "Is your SEO strategy in Mumbai focused on local results?",
      answer:
        "Absolutely. We help you rank for \"near me\" and Mumbai-specific searches, bringing in customers who are actually looking to buy—not just browse.",
    },
  ],
};



    const handleClick = (key) => {
        if (isActive.key === key) {
            setIsActive({
                status: false,
            })
        } else {
            setIsActive({
                status: true,
                key,
            })
        }
    }

    // Function to render FAQ items
    const renderFAQItem = (item, accordionId) => (
        <div className="accordion-item" key={item.id}>
            <h2 className="accordion-header" id={`flush-heading${item.id}`} onClick={() => handleClick(item.id)}>
                <button 
                    className={isActive.key === item.id ? "accordion-button" : "accordion-button collapsed"}
                    type="button" 
                    data-bs-toggle="collapse" 
                    data-bs-target={`#flush-collapse${item.id}`} 
                    aria-expanded="false" 
                    aria-controls={`flush-collapse${item.id}`}
                >
                    {item.question}
                </button>
            </h2>
            <div 
                className={isActive.key === item.id ? "accordion-collapse collapse show" : "accordion-collapse collapse"} 
                id={`flush-collapse${item.id}`} 
                aria-labelledby={`flush-heading${item.id}`} 
                data-bs-parent={`#${accordionId}`}
            >
                <div className="accordion-body">
                    <p>{item.answer}</p>
                </div>
            </div>
        </div>
    )

    return (
        <>
            <section className="section-box box-faqs">
                <div className="container">
                    <div className="text-center mb-70">
                        <h3 className="heading-2 mb-20">Frequently Asked Questions</h3>
                        
                    </div>
                    <div className="box-2-col-faqs">
                        {/* Left Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS">
                                {faqData.leftColumn.map(item => renderFAQItem(item, 'accordionFAQS'))}
                            </div>
                        </div>
                        
                        {/* Right Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS2">
                                {faqData.rightColumn.map(item => renderFAQItem(item, 'accordionFAQS2'))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}