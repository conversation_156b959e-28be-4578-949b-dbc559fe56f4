'use client'
import Link from 'next/link'
import { useState } from 'react'

export default function Section10() {
    const [isActive, setIsActive] = useState({
        status: false,
        key: 1,
    })

const faqData = {
  leftColumn: [
    {
      id: 1,
      question: "What makes you a trusted WhatsApp automation company in Gurgaon?",
      answer:
        "We specialize in WhatsApp-based lead nurturing, client support, and real-time follow-ups for Gurgaon businesses. Our automation flows are custom-built to match your industry and goals.",
    },
    {
      id: 2,
      question: "Are your marketing automation services in Gurgaon tailored to specific industries?",
      answer:
        "Yes. We offer industry-specific marketing automation in Gurgaon—whether you're a real estate firm, ed-tech, or service provider—ensuring maximum efficiency with AI-driven workflows.",
    },
    {
      id: 3,
      question: "Do you provide WhatsApp API services for Gurgaon businesses?",
      answer:
        "Absolutely. As a leading WhatsApp API provider in Gurgaon, we help businesses integrate official APIs to send alerts, enable two-way chat, and automate sales follow-ups.",
    },
    {
      id: 4,
      question: "What makes you the best SEO company in Gurgaon?",
      answer:
        "Our local SEO strategies are tailored for Gurgaon-based businesses. We optimize your Google rankings for location-specific keywords and pair them with automation to boost ROI.",
    },
  ],
  rightColumn: [
    {
      id: 5,
      question: "Can I get a new website built by your Gurgaon team?",
      answer:
        "Yes, our Gurgaon-based website development team builds modern, responsive, and SEO-optimized websites—from landing pages to full-scale platforms.",
    },
    {
      id: 6,
      question: "Do you offer lead generation services in Gurgaon?",
      answer:
        "Yes. We’re a full-stack lead generation company in Gurgaon, combining ads, SEO, and WhatsApp funnels to capture and qualify leads 24/7.",
    },
    {
      id: 7,
      question: "What other digital marketing services do you offer in Gurgaon?",
      answer:
        "We cover SEO, Google Ads, Facebook Ads, CRM integration, website design, and WhatsApp marketing—making us a full-service digital marketing agency in Gurgaon.",
    },
  ],
};


    const handleClick = (key) => {
        if (isActive.key === key) {
            setIsActive({
                status: false,
            })
        } else {
            setIsActive({
                status: true,
                key,
            })
        }
    }

    // Function to render FAQ items
    const renderFAQItem = (item, accordionId) => (
        <div className="accordion-item" key={item.id}>
            <h2 className="accordion-header" id={`flush-heading${item.id}`} onClick={() => handleClick(item.id)}>
                <button 
                    className={isActive.key === item.id ? "accordion-button" : "accordion-button collapsed"}
                    type="button" 
                    data-bs-toggle="collapse" 
                    data-bs-target={`#flush-collapse${item.id}`} 
                    aria-expanded="false" 
                    aria-controls={`flush-collapse${item.id}`}
                >
                    {item.question}
                </button>
            </h2>
            <div 
                className={isActive.key === item.id ? "accordion-collapse collapse show" : "accordion-collapse collapse"} 
                id={`flush-collapse${item.id}`} 
                aria-labelledby={`flush-heading${item.id}`} 
                data-bs-parent={`#${accordionId}`}
            >
                <div className="accordion-body">
                    <p>{item.answer}</p>
                </div>
            </div>
        </div>
    )

    return (
        <>
            <section className="section-box box-faqs">
                <div className="container">
                    <div className="text-center mb-70">
                        <h3 className="heading-2 mb-20">Frequently Asked Questions</h3>
                        
                    </div>
                    <div className="box-2-col-faqs">
                        {/* Left Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS">
                                {faqData.leftColumn.map(item => renderFAQItem(item, 'accordionFAQS'))}
                            </div>
                        </div>
                        
                        {/* Right Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS2">
                                {faqData.rightColumn.map(item => renderFAQItem(item, 'accordionFAQS2'))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}