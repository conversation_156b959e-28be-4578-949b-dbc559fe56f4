'use client'
import Link from 'next/link'
import { useState } from 'react'

export default function Section10() {
    const [isActive, setIsActive] = useState({
        status: false,
        key: 1,
    })

    const faqData = {
  leftColumn: [
    {
      id: 1,
      question: "What makes your WhatsApp automation company in Delhi different?",
      answer:
        "We’re Delhi-based and results-first: our AI-powered WhatsApp automations—from auto-responders to drip sequences—are built to support SEO-led campaigns and maximize conversions.",
    },
    {
      id: 2,
      question: "Can you handle SEO services in Delhi alongside WhatsApp automation?",
      answer:
        "Absolutely. As a top SEO services provider in Delhi, we align our technical, on-page, and local SEO strategies with WhatsApp automation efforts to drive qualified traffic and instant engagement.",
    },
    {
      id: 3,
      question: "Are you both an SEO company and a WhatsApp API provider in Delhi?",
      answer:
        "Yes—we combine the roles of best SEO company in Delhi and trusted WhatsApp API provider, delivering holistic solutions that rank your site, automate your messaging, and nurture leads—all in one place.",
    },
    {
      id: 4,
      question: "I need help building a website—do you offer web design and development in Delhi?",
      answer:
        "Yes, as a leading website development company in Delhi, we design mobile‑optimized, SEO-ready websites integrated with WhatsApp automations—so your site not only looks great but also converts leads from day one.",
    },
  ],
  rightColumn: [
    {
      id: 5,
      question: "How much do your SEO and WhatsApp automation services cost in Delhi?",
      answer:
        "We customize pricing based on scope. Basic SEO and WhatsApp automation packages in Delhi start with keyword research, on-page tweaks, WhatsApp API integration, and monthly performance reporting. Contact us for a personalized quote.",
    },
    {
      id: 6,
      question: "How do you measure the results of your SEO company in Delhi?",
      answer:
        "We track improvements in local keyword rankings (like \"best SEO company in Delhi\"), organic traffic from Delhi searches, bounce rate, conversions, and lead volume. Plus, our WhatsApp integrations provide real-time chat and conversion analytics.",
    },
    {
      id: 7,
      question: "Can your digital marketing agency in Delhi run ads along with automation?",
      answer:
        "Yes—our digital marketing agency in Delhi manages end-to-end SEO, PPC (Google, Meta), and WhatsApp automation campaigns. This ensures your paid traffic is captured, nurtured, and converted via tailored chatflows.",
    },
  ],
};



    const handleClick = (key) => {
        if (isActive.key === key) {
            setIsActive({
                status: false,
            })
        } else {
            setIsActive({
                status: true,
                key,
            })
        }
    }

    // Function to render FAQ items
    const renderFAQItem = (item, accordionId) => (
        <div className="accordion-item" key={item.id}>
            <h2 className="accordion-header" id={`flush-heading${item.id}`} onClick={() => handleClick(item.id)}>
                <button 
                    className={isActive.key === item.id ? "accordion-button" : "accordion-button collapsed"}
                    type="button" 
                    data-bs-toggle="collapse" 
                    data-bs-target={`#flush-collapse${item.id}`} 
                    aria-expanded="false" 
                    aria-controls={`flush-collapse${item.id}`}
                >
                    {item.question}
                </button>
            </h2>
            <div 
                className={isActive.key === item.id ? "accordion-collapse collapse show" : "accordion-collapse collapse"} 
                id={`flush-collapse${item.id}`} 
                aria-labelledby={`flush-heading${item.id}`} 
                data-bs-parent={`#${accordionId}`}
            >
                <div className="accordion-body">
                    <p>{item.answer}</p>
                </div>
            </div>
        </div>
    )

    return (
        <>
            <section className="section-box box-faqs">
                <div className="container">
                    <div className="text-center mb-70">
                        <h3 className="heading-2 mb-20">Frequently Asked Questions</h3>
                        
                    </div>
                    <div className="box-2-col-faqs">
                        {/* Left Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS">
                                {faqData.leftColumn.map(item => renderFAQItem(item, 'accordionFAQS'))}
                            </div>
                        </div>
                        
                        {/* Right Column */}
                        <div className="faqs-col">
                            <div className="accordion accordion-flush" id="accordionFAQS2">
                                {faqData.rightColumn.map(item => renderFAQItem(item, 'accordionFAQS2'))}
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </>
    )
}